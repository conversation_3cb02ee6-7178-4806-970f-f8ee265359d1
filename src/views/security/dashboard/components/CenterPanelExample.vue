<template>
  <div class="center-panel-example">
    <div class="controls">
      <h3>地图控制选项</h3>
      <div class="control-group">
        <label>
          <input v-model="enableZoom" type="checkbox" />
          启用缩放功能
        </label>
      </div>
      <div class="control-group">
        <label>
          <input v-model="enableRotate" type="checkbox" />
          启用旋转拖动功能
        </label>
      </div>
    </div>
    
    <!-- 使用修改后的CenterPanel组件 -->
    <CenterPanel 
      :enable-zoom="enableZoom" 
      :enable-rotate="enableRotate" 
    />
  </div>
</template>

<script setup>
  import { ref } from 'vue';
  import CenterPanel from './CenterPanel.vue';

  // 控制地图功能的响应式变量
  const enableZoom = ref(true);
  const enableRotate = ref(true);
</script>

<style scoped>
  .center-panel-example {
    height: 100vh;
    display: flex;
    flex-direction: column;
  }

  .controls {
    padding: 20px;
    background-color: rgba(0, 0, 0, 0.1);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  }

  .controls h3 {
    margin: 0 0 15px 0;
    color: #fff;
    font-size: 16px;
  }

  .control-group {
    margin-bottom: 10px;
  }

  .control-group label {
    display: flex;
    align-items: center;
    color: #fff;
    font-size: 14px;
    cursor: pointer;
  }

  .control-group input[type="checkbox"] {
    margin-right: 8px;
  }
</style>
